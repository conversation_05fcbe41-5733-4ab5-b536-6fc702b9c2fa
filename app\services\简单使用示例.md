# 服务层使用示例

## 1. 基础设置

```python
from app.services import DatabaseService, VectorService, SearchService
from app.config.settings import VectorDBConfig

# 配置数据库
config = VectorDBConfig(
    db_type="milvus",
    host="localhost", 
    port=19530,
    database="default"
)
```

## 2. 数据库管理示例

### 创建数据库服务并初始化

```python
async def setup_database():
    """设置数据库和集合"""
    
    # 初始化数据库服务
    db_service = DatabaseService()
    await db_service.initialize(config)
    
    # 创建集合
    result = await db_service.create_collection(
        collection_name="knowledge_base",
        vector_dimension=768,
        auto_id=True,
        enable_dynamic_field=True
    )
    
    print(f"集合创建结果: {result}")
    return db_service
```

### 创建混合检索集合

```python
async def setup_hybrid_collection():
    """创建混合检索集合"""
    
    db_service = DatabaseService()
    await db_service.initialize(config)
    
    # 创建混合检索集合
    result = await db_service.create_hybrid_collection(
        collection_name="hybrid_knowledge",
        dense_vector_dim=768
    )
    
    print(f"混合检索集合创建结果: {result}")
    return result
```

## 3. 向量操作示例

### 插入向量数据

```python
async def insert_documents():
    """插入文档向量"""
    
    # 初始化向量服务
    vector_service = VectorService()
    await vector_service.initialize(config)
    
    # 准备文档数据
    texts = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的核心技术",
        "深度学习使用神经网络进行模式识别"
    ]
    
    metadata = [
        {"category": "AI", "author": "研究员A"},
        {"category": "ML", "author": "研究员B"},
        {"category": "DL", "author": "研究员C"}
    ]
    
    # 插入向量
    result = await vector_service.insert_vectors(
        collection_name="knowledge_base",
        texts=texts,
        metadata=metadata,
        auto_flush=True
    )
    
    print(f"向量插入结果: {result}")
    return result
```

### 批量插入大量数据

```python
async def batch_insert_large_dataset():
    """批量插入大量文档"""
    
    vector_service = VectorService()
    await vector_service.initialize(config)
    
    # 模拟大量文本数据
    large_texts = [f"这是第{i}篇关于AI的文档内容..." for i in range(1000)]
    
    # 批量插入
    result = await vector_service.batch_insert_vectors(
        collection_name="knowledge_base",
        texts=large_texts,
        batch_size=100,  # 每批100条
        auto_flush=True
    )
    
    print(f"批量插入结果: {result}")
    return result
```

## 4. 搜索功能示例

### 基础向量搜索

```python
async def basic_search():
    """基础向量搜索"""
    
    # 初始化搜索服务
    search_service = SearchService()
    await search_service.initialize(config)
    
    # 执行搜索
    result = await search_service.search_vectors(
        collection_name="knowledge_base",
        query="什么是机器学习？",
        top_k=5
    )
    
    print(f"搜索结果: {result}")
    return result
```

### 多集合搜索

```python
async def multi_collection_search():
    """多集合搜索"""
    
    search_service = SearchService()
    await search_service.initialize(config)
    
    # 搜索多个集合
    result = await search_service.search_multiple_collections(
        collections=["knowledge_base", "hybrid_knowledge", "documents"],
        query="深度学习的应用",
        top_k=3
    )
    
    print(f"多集合搜索结果: {result}")
    return result
```

### 分页搜索

```python
async def paginated_search():
    """分页搜索"""
    
    search_service = SearchService()
    await search_service.initialize(config)
    
    # 第一页结果
    page1 = await search_service.search_with_pagination(
        collection_name="knowledge_base",
        query="人工智能",
        page=1,
        page_size=10
    )
    
    # 第二页结果
    page2 = await search_service.search_with_pagination(
        collection_name="knowledge_base", 
        query="人工智能",
        page=2,
        page_size=10
    )
    
    print(f"第一页结果: {page1}")
    print(f"第二页结果: {page2}")
    return page1, page2
```

## 5. 混合检索示例

### 语义+全文混合检索

```python
async def hybrid_search():
    """混合检索示例"""
    
    from app.services import HybridSearchService
    
    # 初始化混合检索服务
    hybrid_service = HybridSearchService()
    await hybrid_service.initialize(config)
    
    # 执行混合检索
    result = await hybrid_service.hybrid_search(
        collection_name="hybrid_knowledge",
        query="深度学习在图像识别中的应用",
        top_k=10,
        dense_weight=0.7,     # 语义搜索权重
        sparse_weight=0.3,    # 全文搜索权重
        search_strategy="rrf" # 使用RRF融合策略
    )
    
    print(f"混合检索结果: {result}")
    return result
```

### 纯语义搜索

```python
async def semantic_only_search():
    """纯语义搜索"""
    
    from app.services import HybridSearchService
    
    hybrid_service = HybridSearchService()
    await hybrid_service.initialize(config)
    
    # 仅使用语义搜索
    result = await hybrid_service.semantic_only_search(
        collection_name="hybrid_knowledge",
        query="AI technology advancement",
        top_k=5
    )
    
    print(f"语义搜索结果: {result}")
    return result
```

## 6. 文档处理示例

### 上传文本列表

```python
async def upload_text_documents():
    """上传文本文档"""
    
    from app.services import DocumentService
    
    # 初始化文档服务
    doc_service = DocumentService()
    await doc_service.initialize(config)
    
    # 准备文档内容
    documents = [
        "自然语言处理是人工智能的重要分支，专注于让计算机理解和生成人类语言。",
        "计算机视觉技术能够让机器理解和分析视觉信息，在自动驾驶中应用广泛。",
        "推荐系统通过分析用户行为和偏好，为用户提供个性化的内容推荐。"
    ]
    
    metadata = [
        {"topic": "NLP", "difficulty": "中级"},
        {"topic": "CV", "difficulty": "高级"},
        {"topic": "RecSys", "difficulty": "中级"}
    ]
    
    # 上传文档
    result = await doc_service.upload_texts(
        texts=documents,
        collection_name="knowledge_base",
        metadata=metadata,
        auto_flush=True
    )
    
    print(f"文档上传结果: {result}")
    return result
```

## 7. 完整工作流示例

### 端到端文档处理和搜索

```python
async def complete_workflow():
    """完整的文档处理和搜索工作流"""
    
    # 1. 初始化所有服务
    db_service = DatabaseService()
    doc_service = DocumentService()
    search_service = SearchService()
    
    await db_service.initialize(config)
    await doc_service.initialize(config)
    await search_service.initialize(config)
    
    try:
        # 2. 创建集合
        await db_service.create_collection(
            collection_name="demo_collection",
            vector_dimension=768
        )
        print("✓ 集合创建成功")
        
        # 3. 上传文档
        documents = [
            "量子计算是利用量子力学原理进行计算的新兴技术",
            "区块链技术提供了去中心化的数据存储和验证方案",
            "边缘计算将计算能力分布到网络边缘，减少延迟"
        ]
        
        upload_result = await doc_service.upload_texts(
            texts=documents,
            collection_name="demo_collection"
        )
        print(f"✓ 文档上传成功: {upload_result['message']}")
        
        # 4. 执行搜索
        search_result = await search_service.search_vectors(
            collection_name="demo_collection",
            query="什么是量子技术？",
            top_k=3
        )
        print(f"✓ 搜索完成: 找到 {len(search_result['data']['results'])} 条结果")
        
        # 5. 显示搜索结果
        for i, result in enumerate(search_result['data']['results']):
            print(f"结果 {i+1}: {result.get('content', '')[:50]}...")
            print(f"相似度: {result.get('similarity', 0):.3f}")
        
        return search_result
        
    except Exception as e:
        print(f"❌ 工作流执行失败: {e}")
        raise
    finally:
        # 6. 清理资源
        db_service.close_connections()
        print("✓ 资源清理完成")
```

## 8. 错误处理示例

### 优雅的错误处理

```python
async def robust_search_with_fallback():
    """带有错误处理和降级的搜索"""
    
    search_service = SearchService()
    
    try:
        await search_service.initialize(config)
        
        # 尝试高级搜索
        result = await search_service.search_multiple_collections(
            collections=["collection1", "collection2", "collection3"],
            query="人工智能",
            top_k=10
        )
        return result
        
    except Exception as e:
        print(f"多集合搜索失败: {e}")
        
        try:
            # 降级到单集合搜索
            result = await search_service.search_vectors(
                collection_name="collection1",
                query="人工智能",
                top_k=10
            )
            print("✓ 使用单集合搜索作为后备方案")
            return result
            
        except Exception as e2:
            print(f"单集合搜索也失败: {e2}")
            return {
                "success": False,
                "message": "搜索服务暂时不可用",
                "data": {"results": []}
            }
```

## 9. 批量操作示例

### 批量数据处理

```python
async def batch_data_processing():
    """批量数据处理示例"""
    
    vector_service = VectorService()
    await vector_service.initialize(config)
    
    # 准备大量数据
    texts_batch1 = [f"第一批文档 {i}" for i in range(100)]
    texts_batch2 = [f"第二批文档 {i}" for i in range(100)]
    texts_batch3 = [f"第三批文档 {i}" for i in range(100)]
    
    # 并发批量插入
    import asyncio
    
    tasks = [
        vector_service.batch_insert_vectors("collection1", texts_batch1, batch_size=20),
        vector_service.batch_insert_vectors("collection2", texts_batch2, batch_size=20),
        vector_service.batch_insert_vectors("collection3", texts_batch3, batch_size=20)
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"批次 {i+1} 处理失败: {result}")
        else:
            print(f"批次 {i+1} 处理成功: {result['message']}")
    
    return results
```

## 10. 使用建议

### 最佳实践

```python
# 1. 使用配置管理
async def best_practice_example():
    """最佳实践示例"""
    
    # 从环境变量或配置文件加载配置
    config = VectorDBConfig.from_env()
    
    # 使用上下文管理器（如果可用）
    services = {
        'db': DatabaseService(),
        'vector': VectorService(),
        'search': SearchService()
    }
    
    try:
        # 初始化所有服务
        for name, service in services.items():
            await service.initialize(config)
            print(f"✓ {name} 服务初始化成功")
        
        # 执行业务逻辑
        # ... 你的业务代码 ...
        
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        raise
    finally:
        # 清理资源
        for service in services.values():
            if hasattr(service, 'close_connections'):
                service.close_connections()
```

## 运行示例

要运行这些示例，请确保：

1. **数据库已启动**: Milvus/其他向量数据库服务正在运行
2. **配置正确**: 数据库连接信息配置正确
3. **依赖安装**: 所有必要的依赖包已安装

```bash
# 运行示例
python -c "
import asyncio
from app.services.examples import complete_workflow
asyncio.run(complete_workflow())
"
```

这些示例展示了服务层的主要功能和使用模式，你可以根据具体需求调整参数和逻辑。 