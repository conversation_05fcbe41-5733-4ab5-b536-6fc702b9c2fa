# Repository层使用指南 - 如何创建向量库

本文档展示如何使用我们实现的repository层来创建和操作向量库。

## 架构概述

我们的repository层提供了以下组件：

```
app/repositories/
├── __init__.py                  # 导出主要类
├── database_repository.py       # 数据库连接管理
├── collection_repository.py     # 集合管理
├── vector_repository.py         # 向量操作
├── search_repository.py         # 搜索操作
└── hybrid_repository.py         # 混合检索操作
```

## 基本使用流程

### 1. 初始化数据库连接

```python
from app.config.settings import VectorDBConfig
from app.repositories import VectorDatabaseRepository

# 创建数据库配置
config = VectorDBConfig(
    db_type="milvus",
    uri="http://localhost:19530",  # 根据实际情况修改
    token=""  # 如果需要认证，填入token
)

# 初始化数据库repository
db_repo = VectorDatabaseRepository()
db_repo.set_config(config)

# 获取数据库连接（自动创建数据库）
db_connection = await db_repo.get_database_connection(
    database="my_vector_db", 
    create_if_not_exists=True
)
```

### 2. 创建集合

```python
from app.repositories import CollectionRepository

# 初始化集合repository
collection_repo = CollectionRepository(db_connection)

# 创建简单向量集合
await collection_repo.create_collection(
    collection_name="documents",
    vector_dimension=768,
    auto_id=True,           # 自动生成ID
    enable_dynamic_field=True  # 启用动态字段
)

# 创建混合检索集合（支持全文+语义检索）
await collection_repo.create_hybrid_collection(
    collection_name="hybrid_documents",
    dense_vector_dim=768,
    auto_id=True,
    enable_dynamic_field=True
)
```

### 3. 插入向量数据

```python
from app.repositories import VectorRepository

# 初始化向量repository
vector_repo = VectorRepository(db_connection)

# 准备数据
texts = ["这是第一个文档", "这是第二个文档"]
vectors = [[0.1] * 768, [0.2] * 768]  # 实际应用中由embedding模型生成
metadata_list = [
    {"category": "技术", "source": "文档1"},
    {"category": "技术", "source": "文档2"}
]

# 构建向量记录
records = []
for text, vector, metadata in zip(texts, vectors, metadata_list):
    record = vector_repo.build_normal_vector_record(
        content=text,
        vector=vector,
        metadata=metadata
    )
    records.append(record)

# 插入向量
result = await vector_repo.insert_vectors("documents", records)
print(f"成功插入 {result} 条记录")
```

### 4. 插入混合检索向量

```python
# 构建混合检索向量记录
hybrid_records = []
for text, vector, metadata in zip(texts, vectors, metadata_list):
    record = vector_repo.build_hybrid_vector_record(
        content=text,          # 存储的内容
        text=text,            # 原始文本（用于BM25全文检索）
        dense_vector=vector,   # 密集向量（用于语义检索）
        metadata=metadata
    )
    hybrid_records.append(record)

# 插入混合检索向量
result = await vector_repo.insert_hybrid_vectors("hybrid_documents", hybrid_records)
print(f"成功插入 {result} 条混合检索记录")
```

### 5. 搜索向量

```python
from app.repositories import SearchRepository

# 初始化搜索repository
search_repo = SearchRepository(db_connection)

# 准备查询向量
query_vector = [0.15] * 768  # 实际应用中由embedding模型生成

# 普通向量搜索
results = await search_repo.search_vectors(
    collection_name="documents",
    query_vector=query_vector,
    top_k=5
)

# 带筛选条件的搜索
filtered_results = await search_repo.search_with_filters(
    collection_name="documents",
    query_vector=query_vector,
    top_k=5,
    metadata_filters={"category": "技术"}
)

# 格式化结果
formatted_results = await search_repo.format_search_results(results)
```

### 6. 混合检索搜索

```python
from app.repositories import HybridRepository

# 初始化混合检索repository
hybrid_repo = HybridRepository(db_connection)

# 混合检索（语义+全文）
hybrid_results = await hybrid_repo.search_hybrid_vectors(
    collection_name="hybrid_documents",
    query_text="查询文本",
    dense_vector=query_vector,
    top_k=5,
    search_strategy="hybrid"  # 'semantic', 'full_text', 'hybrid'
)

# 仅语义检索
semantic_results = await hybrid_repo.search_semantic_only(
    collection_name="hybrid_documents",
    dense_vector=query_vector,
    top_k=5
)

# 仅全文检索
fulltext_results = await hybrid_repo.search_full_text_only(
    collection_name="hybrid_documents",
    query_text="查询文本",
    top_k=5
)
```

### 7. 批量操作

```python
# 批量插入
batch_records = [
    vector_repo.build_normal_vector_record(
        content=f"文档 {i}",
        vector=[0.1 + i*0.01] * 768,
        metadata={"batch_id": i}
    )
    for i in range(100)
]

total_inserted = await vector_repo.batch_insert_vectors(
    collection_name="documents",
    records=batch_records,
    batch_size=20
)

# 并发插入（适合大量数据）
total_inserted = await vector_repo.concurrent_insert_vectors(
    collection_name="documents",
    records=batch_records,
    batch_size=20,
    max_concurrent=3
)
```

### 8. 集合管理

```python
# 检查集合是否存在
exists = await collection_repo.collection_exists("documents")

# 获取集合信息
info = await collection_repo.get_collection_info("documents")

# 获取集合统计
stats = await collection_repo.get_collection_stats("documents")

# 列出所有集合
collections = await collection_repo.list_collections()

# 刷新集合（确保数据持久化）
await collection_repo.flush_collection("documents")

# 加载集合到内存
await collection_repo.load_collection("documents")
```

### 9. 向量操作

```python
# 统计向量数量
count = await vector_repo.count_vectors("documents")

# Upsert操作（更新或插入）
upsert_records = [
    {
        "id": 1,  # 指定ID进行更新
        "content": "更新后的内容",
        "vector": [0.5] * 768,
        "metadata": {"updated": True}
    }
]
result = await vector_repo.upsert_vectors("documents", upsert_records)

# 删除向量（按ID）
await vector_repo.delete_vectors("documents", ids=[1, 2, 3])

# 删除向量（按条件）
await vector_repo.delete_vectors(
    "documents", 
    filter_expr="metadata['category'] == 'old'"
)
```

### 10. 搜索高级功能

```python
# 分页搜索
page_result = await search_repo.search_with_pagination(
    collection_name="documents",
    query_vector=query_vector,
    page=1,
    page_size=10
)

results = page_result["results"]
pagination = page_result["pagination"]

# 重排序搜索
reranked_results = await search_repo.search_with_rerank(
    collection_name="documents",
    query_vector=query_vector,
    top_k=10,
    rerank_factor=2.0  # 先获取20个候选，再重排序选出10个
)

# 搜索多个集合
multi_results = await search_repo.search_multiple_collections(
    collection_names=["documents", "articles"],
    query_vector=query_vector,
    top_k=5
)
```

### 11. 资源清理

```python
# 关闭特定数据库连接
db_repo.close_connection("my_vector_db")

# 关闭所有连接
db_repo.close_all_connections()
```

## 完整示例

这里是一个完整的端到端示例：

```python
import asyncio
from app.config.settings import VectorDBConfig
from app.repositories import (
    VectorDatabaseRepository,
    CollectionRepository,
    VectorRepository,
    SearchRepository
)

async def main():
    # 1. 配置和连接
    config = VectorDBConfig(
        db_type="milvus",
        uri="http://localhost:19530",
        token=""
    )
    
    db_repo = VectorDatabaseRepository()
    db_repo.set_config(config)
    db_connection = await db_repo.get_database_connection(
        database="test_db", 
        create_if_not_exists=True
    )
    
    # 2. 初始化repositories
    collection_repo = CollectionRepository(db_connection)
    vector_repo = VectorRepository(db_connection)
    search_repo = SearchRepository(db_connection)
    
    try:
        # 3. 创建集合
        await collection_repo.create_collection(
            collection_name="test_docs",
            vector_dimension=128
        )
        
        # 4. 插入数据
        test_data = [
            ("文档1内容", [0.1] * 128, {"type": "doc"}),
            ("文档2内容", [0.2] * 128, {"type": "doc"}),
            ("文档3内容", [0.3] * 128, {"type": "doc"})
        ]
        
        records = [
            vector_repo.build_normal_vector_record(
                content=text,
                vector=vector,
                metadata=metadata
            )
            for text, vector, metadata in test_data
        ]
        
        await vector_repo.insert_vectors("test_docs", records)
        
        # 5. 搜索
        query_vector = [0.15] * 128
        results = await search_repo.search_vectors(
            collection_name="test_docs",
            query_vector=query_vector,
            top_k=2
        )
        
        print(f"搜索到 {len(results)} 条结果")
        for i, result in enumerate(results):
            print(f"结果 {i+1}: {result.get('content', '')}")
            
    finally:
        # 6. 清理
        db_repo.close_all_connections()

if __name__ == "__main__":
    asyncio.run(main())
```

## 注意事项

1. **初始化顺序**：必须先设置配置，再获取连接，最后初始化各个repository
2. **错误处理**：所有操作都可能抛出异常，建议使用try-catch包装
3. **资源清理**：使用完毕后要关闭连接
4. **向量维度**：确保所有向量的维度一致
5. **ID管理**：如果使用自定义ID，确保ID的唯一性
6. **批量操作**：大量数据建议使用批量或并发插入
7. **集合类型**：普通集合和混合检索集合的操作方法不同

这个repository层的设计将原来routes.py中的复杂逻辑分层抽象，提高了代码的可维护性和可测试性。 